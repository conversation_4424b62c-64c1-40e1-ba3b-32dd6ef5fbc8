using System;
using System.IO;
using System.Text.Json;

namespace LauncherAutoUpdater
{
    /// <summary>
    /// 更新器配置管理类
    /// </summary>
    public class UpdateConfig
    {
        /// <summary>
        /// 更新服务器地址
        /// </summary>
        public string UpdateServerUrl { get; set; } = "https://your-server.com/launcher_version.xml";

        /// <summary>
        /// 目标启动器程序名称
        /// </summary>
        public string TargetLauncherName { get; set; } = "VIC_Launcher_CloudPlatform.exe";

        /// <summary>
        /// 更新临时文件夹名称
        /// </summary>
        public string UpdateTempFolder { get; set; } = "UpdateTemp";

        /// <summary>
        /// 是否启用自动更新
        /// </summary>
        public bool EnableAutoUpdate { get; set; } = true;

        /// <summary>
        /// 更新检查间隔（分钟）
        /// </summary>
        public int UpdateCheckInterval { get; set; } = 60;

        /// <summary>
        /// 是否显示更新进度窗口
        /// </summary>
        public bool ShowUpdateProgress { get; set; } = true;

        /// <summary>
        /// 配置文件名称
        /// </summary>
        private const string CONFIG_FILE_NAME = "UpdateConfig.json";

        /// <summary>
        /// 加载配置文件
        /// </summary>
        /// <returns>配置对象</returns>
        public static UpdateConfig Load()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CONFIG_FILE_NAME);
                
                if (File.Exists(configPath))
                {
                    string jsonContent = File.ReadAllText(configPath);
                    var config = JsonSerializer.Deserialize<UpdateConfig>(jsonContent);
                    return config ?? new UpdateConfig();
                }
            }
            catch (Exception ex)
            {
                // 配置文件加载失败时使用默认配置
                System.Diagnostics.Debug.WriteLine($"加载配置文件失败：{ex.Message}");
            }

            // 返回默认配置并保存
            var defaultConfig = new UpdateConfig();
            defaultConfig.Save();
            return defaultConfig;
        }

        /// <summary>
        /// 保存配置文件
        /// </summary>
        public void Save()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CONFIG_FILE_NAME);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                string jsonContent = JsonSerializer.Serialize(this, options);
                File.WriteAllText(configPath, jsonContent);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(UpdateServerUrl) &&
                   !string.IsNullOrWhiteSpace(TargetLauncherName) &&
                   !string.IsNullOrWhiteSpace(UpdateTempFolder) &&
                   UpdateCheckInterval > 0;
        }
    }
}
