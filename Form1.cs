namespace LauncherAutoUpdater
{
    /// <summary>
    /// 更新器主窗体
    /// </summary>
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            InitializeUI();
        }

        /// <summary>
        /// 初始化用户界面
        /// </summary>
        private void InitializeUI()
        {
            this.Text = "VIC Launcher CloudPlatform 更新器";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Size = new Size(400, 200);
            this.ShowInTaskbar = false;

            // 设置窗体图标（如果有的话）
            try
            {
                // 可以在这里设置应用程序图标
                // this.Icon = new Icon("app.ico");
            }
            catch
            {
                // 忽略图标加载错误
            }
        }

        /// <summary>
        /// 更新状态文本
        /// </summary>
        /// <param name="message">状态消息</param>
        public void UpdateStatus(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateStatus), message);
                return;
            }

            statusLabel.Text = message;
        }

        /// <summary>
        /// 显示更新进度
        /// </summary>
        /// <param name="percentage">进度百分比</param>
        public void UpdateProgress(int percentage)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int>(UpdateProgress), percentage);
                return;
            }

            progressBar.Value = Math.Max(0, Math.Min(100, percentage));
        }

        /// <summary>
        /// 重置进度条
        /// </summary>
        public void ResetProgress()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(ResetProgress));
                return;
            }

            progressBar.Value = 0;
        }
    }
}
