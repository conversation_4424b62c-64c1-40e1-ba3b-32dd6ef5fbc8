# Unity项目更新包制作指南

## 概述

本指南说明如何为 VIC_Launcher_CloudPlatform Unity项目制作更新包，以便通过自动更新器进行分发。

## Unity项目构建文件结构

根据您提供的Unity构建输出，标准的Unity项目包含以下文件和文件夹：

### 必需文件
- `VIC_Launcher_CloudPlatform.exe` - 主可执行文件
- `UnityPlayer.dll` - Unity播放器库
- `UnityCrashHandler64.exe` - 崩溃处理器

### 必需文件夹
- `VIC_Launcher_CloudPlatform_Data/` - Unity数据文件夹
- `MonoBleedingEdge/` - Mono运行时环境

### 可选文件夹
- `VIC_Launcher_CloudPlatform_BurstDebugInformation_DoNotShip/` - Burst调试信息（发布时可删除）

## 更新包制作步骤

### 1. 准备构建文件

从Unity构建输出目录收集所有必需文件：

```
VIC_Launcher_CloudPlatform/
├── VIC_Launcher_CloudPlatform.exe
├── UnityPlayer.dll
├── UnityCrashHandler64.exe
├── VIC_Launcher_CloudPlatform_Data/
│   ├── Managed/
│   ├── Resources/
│   ├── StreamingAssets/
│   ├── app.info
│   ├── boot.config
│   ├── globalgamemanagers
│   ├── globalgamemanagers.assets
│   ├── level0
│   ├── level0.resS
│   └── ...
└── MonoBleedingEdge/
    ├── EmbedRuntime/
    ├── etc/
    └── ...
```

### 2. 版本管理

#### 更新版本号
1. 在Unity项目中更新版本号（Player Settings > Version）
2. 重新构建项目
3. 记录新的版本号用于配置文件

#### 版本号格式
建议使用语义化版本号：`主版本.次版本.修订版本.构建版本`
- 例如：`*******`

### 3. 创建更新包

#### 压缩包制作
1. 将所有构建文件放入一个文件夹
2. 使用ZIP格式压缩（推荐使用7-Zip或WinRAR）
3. 命名格式：`VIC_Launcher_CloudPlatform_v{版本号}.zip`
4. 例如：`VIC_Launcher_CloudPlatform_v1.2.0.zip`

#### 压缩设置建议
- 压缩级别：标准或最大
- 压缩方法：Deflate
- 确保保持文件夹结构

### 4. 计算文件校验和

使用工具计算ZIP文件的MD5或SHA256校验和：

```bash
# Windows PowerShell
Get-FileHash "VIC_Launcher_CloudPlatform_v1.2.0.zip" -Algorithm MD5

# 或使用在线工具
```

### 5. 上传到服务器

#### 服务器目录结构
```
https://asset.vicfunxr.cn/launcher/cloud/updates/
├── launcher_version.xml          # 版本信息文件
├── VIC_Launcher_CloudPlatform_v1.2.0.zip  # 更新包
├── changelog.html                # 更新日志（可选）
└── archive/                      # 历史版本存档
    ├── VIC_Launcher_CloudPlatform_v1.1.0.zip
    └── VIC_Launcher_CloudPlatform_v1.0.0.zip
```

#### 上传步骤
1. 上传ZIP文件到服务器
2. 更新 `launcher_version.xml` 文件
3. 测试下载链接可访问性

### 6. 配置版本信息文件

创建或更新 `launcher_version.xml`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<item>
    <version>*******</version>
    <url>https://asset.vicfunxr.cn/launcher/cloud/updates/VIC_Launcher_CloudPlatform_v1.2.0.zip</url>
    <changelog>https://asset.vicfunxr.cn/launcher/cloud/updates/changelog.html</changelog>
    <mandatory>false</mandatory>
    <size>52428800</size>
    <checksum algorithm="MD5">实际计算的MD5值</checksum>
</item>
```

#### 参数说明
- `version`: 新版本号
- `url`: ZIP文件的完整下载地址
- `changelog`: 更新日志页面地址（可选）
- `mandatory`: 是否强制更新
- `size`: ZIP文件大小（字节）
- `checksum`: 文件校验和

## 自动化脚本示例

### PowerShell打包脚本

```powershell
# Unity项目自动打包脚本
param(
    [string]$BuildPath = "D:\Unity\Builds\VIC_Launcher_CloudPlatform",
    [string]$Version = "*******",
    [string]$OutputPath = "D:\Updates"
)

$ZipName = "VIC_Launcher_CloudPlatform_v$Version.zip"
$ZipPath = Join-Path $OutputPath $ZipName

# 创建ZIP文件
Compress-Archive -Path "$BuildPath\*" -DestinationPath $ZipPath -Force

# 计算MD5
$Hash = Get-FileHash $ZipPath -Algorithm MD5
$Size = (Get-Item $ZipPath).Length

Write-Host "Package created: $ZipPath"
Write-Host "Size: $Size bytes"
Write-Host "MD5: $($Hash.Hash)"
```

## 测试验证

### 本地测试
1. 在测试环境中部署旧版本
2. 配置更新服务器指向新版本
3. 运行自动更新器验证更新流程
4. 确认更新后程序正常启动

### 检查清单
- [ ] 所有必需文件都包含在ZIP中
- [ ] 文件夹结构正确
- [ ] 版本号正确更新
- [ ] ZIP文件可以正常下载
- [ ] 校验和计算正确
- [ ] 更新后程序正常运行

## 故障排除

### 常见问题

1. **更新后程序无法启动**
   - 检查所有依赖文件是否完整
   - 验证文件权限设置
   - 确认Unity运行时环境正确

2. **下载失败**
   - 检查服务器文件权限
   - 验证URL可访问性
   - 确认网络连接稳定

3. **校验和不匹配**
   - 重新计算文件校验和
   - 检查文件是否在传输中损坏
   - 验证压缩过程是否正确

## 最佳实践

1. **版本管理**
   - 保持版本号的一致性
   - 维护版本发布记录
   - 保留历史版本备份

2. **文件管理**
   - 使用一致的命名规范
   - 定期清理过期版本
   - 监控服务器存储空间

3. **测试流程**
   - 在发布前充分测试
   - 使用分阶段发布策略
   - 准备回滚方案

4. **安全考虑**
   - 使用HTTPS传输
   - 验证文件完整性
   - 限制服务器访问权限
