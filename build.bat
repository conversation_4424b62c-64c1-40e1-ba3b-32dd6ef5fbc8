@echo off
echo ========================================
echo VIC Launcher CloudPlatform 更新器构建脚本
echo ========================================

REM 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到 .NET SDK，请先安装 .NET 8.0 SDK
    pause
    exit /b 1
)

echo 正在清理旧的构建文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo 正在还原 NuGet 包...
dotnet restore
if %errorlevel% neq 0 (
    echo 错误：NuGet 包还原失败
    pause
    exit /b 1
)

echo 正在构建项目（Release 模式）...
dotnet build --configuration Release --no-restore
if %errorlevel% neq 0 (
    echo 错误：项目构建失败
    pause
    exit /b 1
)

echo 正在发布项目...
dotnet publish --configuration Release --output "publish" --no-build --self-contained false
if %errorlevel% neq 0 (
    echo 错误：项目发布失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo 发布文件位置：%cd%\publish
echo.
echo 部署说明：
echo 1. 将 publish 目录中的所有文件复制到目标位置
echo 2. 确保目标位置包含 VIC_Launcher_CloudPlatform.exe
echo 3. 根据需要修改 UpdateConfig.json 中的服务器地址
echo 4. 运行 LauncherAutoUpdater.exe 开始更新检查
echo.
pause
