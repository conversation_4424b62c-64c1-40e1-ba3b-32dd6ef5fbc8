# VIC Launcher CloudPlatform 更新器部署指南

## 快速开始

### 1. 构建项目

运行构建脚本：
```bash
build.bat
```

或者手动构建：
```bash
dotnet build --configuration Release
dotnet publish --configuration Release --output publish
```

### 2. 配置更新服务器

编辑 `UpdateConfig.json` 文件：
```json
{
  "UpdateServerUrl": "https://your-actual-server.com/launcher_version.xml",
  "TargetLauncherName": "VIC_Launcher_CloudPlatform.exe",
  "UpdateTempFolder": "UpdateTemp",
  "EnableAutoUpdate": true,
  "UpdateCheckInterval": 60,
  "ShowUpdateProgress": true
}
```

### 3. 部署文件

将以下文件复制到目标目录：
- `LauncherAutoUpdater.exe`
- `UpdateConfig.json`
- 所有依赖的 DLL 文件
- 确保目标目录中有 `VIC_Launcher_CloudPlatform.exe`

## 服务器端配置

### 1. 创建版本信息文件

在您的服务器上创建 `launcher_version.xml` 文件：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<item>
    <version>*******</version>
    <url>https://your-server.com/releases/VIC_Launcher_CloudPlatform_v1.2.0.zip</url>
    <changelog>https://your-server.com/changelog.html</changelog>
    <mandatory>false</mandatory>
    <size>52428800</size>
    <checksum algorithm="MD5">5d41402abc4b2a76b9719d911017c592</checksum>
</item>
```

### 2. 准备更新包

创建包含新版本文件的 ZIP 压缩包：
- 压缩包应包含 `VIC_Launcher_CloudPlatform.exe` 和相关文件
- 确保文件结构与目标目录一致
- 建议使用版本号命名压缩包

### 3. 服务器要求

- 支持 HTTP/HTTPS 访问
- 能够提供静态文件下载
- 建议启用 CORS（如果需要跨域访问）

## 高级配置

### 自定义更新逻辑

如需自定义更新行为，可以修改 `Program.cs` 中的以下方法：

1. `ConfigureAutoUpdater()` - 配置更新器参数
2. `AutoUpdater_CheckForUpdateEvent()` - 处理更新检查结果
3. `AutoUpdater_ApplicationExitEvent()` - 处理更新完成事件

### 添加日志记录

可以集成日志库（如 NLog 或 Serilog）来记录更新过程：

```csharp
// 在 Program.cs 中添加
private static readonly ILogger _logger = LogManager.GetCurrentClassLogger();

// 在关键位置添加日志
_logger.Info("开始检查更新");
_logger.Error($"更新失败：{ex.Message}");
```

### 多语言支持

可以通过资源文件实现多语言支持：

1. 添加资源文件（如 `Resources.resx`）
2. 创建不同语言的资源文件（如 `Resources.zh-CN.resx`）
3. 在代码中使用资源字符串

## 故障排除

### 常见问题

1. **"找不到目标程序"错误**
   - 确保 `VIC_Launcher_CloudPlatform.exe` 存在于同一目录
   - 检查 `UpdateConfig.json` 中的 `TargetLauncherName` 配置

2. **"无法连接到更新服务器"错误**
   - 检查网络连接
   - 验证 `UpdateServerUrl` 是否正确
   - 确认服务器是否可访问

3. **更新下载失败**
   - 检查磁盘空间
   - 验证下载 URL 是否有效
   - 检查防火墙和杀毒软件设置

4. **权限问题**
   - 确保程序有写入权限
   - 考虑以管理员身份运行
   - 检查 UAC 设置

### 调试模式

在开发环境中，可以启用调试模式：

```csharp
#if DEBUG
AutoUpdater.ReportErrors = true;
AutoUpdater.ShowSkipButton = true;
AutoUpdater.ShowRemindLaterButton = true;
#endif
```

### 日志文件位置

- 应用程序日志：`%TEMP%\LauncherAutoUpdater.log`
- 更新器临时文件：`UpdateTemp` 目录
- 配置文件：应用程序目录下的 `UpdateConfig.json`

## 安全考虑

1. **HTTPS 连接**：建议使用 HTTPS 来保护更新过程
2. **文件校验**：使用 MD5 或 SHA256 校验下载文件完整性
3. **数字签名**：考虑对可执行文件进行数字签名
4. **权限控制**：限制更新器的文件系统访问权限

## 性能优化

1. **增量更新**：只下载变更的文件
2. **压缩传输**：使用 gzip 压缩减少下载时间
3. **并行下载**：支持多线程下载大文件
4. **缓存机制**：缓存版本信息减少服务器请求

## 监控和分析

建议实施以下监控：

1. **更新成功率**：跟踪更新成功和失败的比例
2. **下载速度**：监控下载性能
3. **错误日志**：收集和分析错误信息
4. **用户反馈**：建立用户反馈机制

## 版本管理

建议的版本号格式：`主版本.次版本.修订版本.构建版本`

- **主版本**：重大功能变更
- **次版本**：新功能添加
- **修订版本**：错误修复
- **构建版本**：构建编号

## 联系支持

如遇到问题，请提供以下信息：

1. 错误消息的完整文本
2. 操作系统版本
3. .NET 运行时版本
4. 配置文件内容
5. 相关日志文件
