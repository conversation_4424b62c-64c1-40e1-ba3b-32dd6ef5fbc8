# VIC Launcher CloudPlatform 自动更新器 - 发布版本

## 📁 项目文件说明

### 核心代码文件
- **Program.cs** - 主程序入口和更新逻辑
- **Form1.cs** - 用户界面
- **Form1.Designer.cs** - 界面设计器代码
- **UpdateConfig.cs** - 配置管理类
- **UnityUpdateHelper.cs** - Unity项目专用辅助类

### 项目配置文件
- **LauncherAutoUpdater.csproj** - 项目配置文件
- **LauncherAutoUpdater.sln** - 解决方案文件
- **UpdateConfig.json** - 运行时配置文件

### 服务器端配置模板
- **launcher_version_template.xml** - 通用版本信息模板
- **launcher_version_example.xml** - 实际配置示例

### 文档文件
- **README.md** - 项目主要文档
- **DEPLOYMENT.md** - 部署指南
- **UNITY_PACKAGING_GUIDE.md** - Unity项目打包指南
- **PROJECT_STATUS.md** - 项目状态报告
- **发布说明.md** - 本文件

## 🚀 编译和发布

### 编译项目
```bash
dotnet build --configuration Release
```

### 发布为单个exe文件（需要网络）
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o publish-single
```

### 发布为依赖框架的版本
```bash
dotnet publish -c Release -o publish-framework
```

## 📦 部署说明

### 依赖框架版本（推荐）
1. 编译项目：`dotnet build -c Release`
2. 复制 `bin\Release\net8.0-windows\` 目录中的所有文件
3. 确保目标机器安装了 .NET 8.0 运行时
4. 配置 `UpdateConfig.json` 中的服务器地址

### 自包含版本
1. 使用 `dotnet publish` 命令发布
2. 复制发布目录中的所有文件
3. 无需目标机器安装 .NET 运行时

## ⚙️ 配置说明

### 客户端配置 (UpdateConfig.json)
```json
{
  "UpdateServerUrl": "https://asset.vicfunxr.cn/launcher/cloud/updates/launcher_version.xml",
  "TargetLauncherName": "VIC_Launcher_CloudPlatform.exe",
  "UpdateTempFolder": "UpdateTemp",
  "EnableAutoUpdate": true,
  "UpdateCheckInterval": 60,
  "ShowUpdateProgress": true
}
```

### 服务器端配置
参考 `launcher_version_example.xml` 文件，在服务器上创建对应的版本信息文件。

## 📋 使用流程

1. **开发者**：
   - 修改代码后编译项目
   - 按照 Unity 打包指南制作更新包
   - 上传到服务器并更新版本信息文件

2. **用户**：
   - 运行 `LauncherAutoUpdater.exe`
   - 程序自动检查更新并下载安装
   - 更新完成后自动启动 Unity 程序

## 🔧 技术支持

- 项目基于 .NET 8.0 Windows Forms
- 使用 AutoUpdater.NET.Official 库
- 支持 Unity 项目的完整文件结构更新
- 包含完善的错误处理和用户反馈

---

**版本**: v1.3.0  
**状态**: 生产就绪  
**最后更新**: 2025年8月5日
