# VIC Launcher CloudPlatform 自动更新器

## 项目简介

这是一个专为 VIC_Launcher_CloudPlatform Unity 项目设计的自动更新工具。该工具能够自动检测、下载并安装 VIC_Launcher_CloudPlatform.exe 的最新版本，确保用户始终使用最新版本的启动器。

## 功能特性

- **自动版本检测**：定期检查服务器上的最新版本信息
- **智能下载**：仅在有新版本时才进行下载，节省带宽
- **安全更新**：下载完成后自动验证文件完整性
- **无缝切换**：更新完成后自动启动新版本的 VIC_Launcher_CloudPlatform.exe
- **错误处理**：完善的错误处理和日志记录机制
- **用户友好**：简洁的界面和清晰的更新进度提示

## 技术架构

- **框架**：.NET 8.0 Windows Forms
- **更新库**：AutoUpdater.NET.Official v1.9.2
- **目标平台**：Windows
- **目标程序**：VIC_Launcher_CloudPlatform.exe (Unity 项目)

## 使用方法

### 基本使用

1. 将 `LauncherAutoUpdater.exe` 放置在与 `VIC_Launcher_CloudPlatform.exe` 相同的目录中
2. 双击运行 `LauncherAutoUpdater.exe`
3. 程序将自动检查更新并在需要时进行更新
4. 更新完成后会自动启动 `VIC_Launcher_CloudPlatform.exe`

### 配置说明

#### 更新服务器配置

在 `Program.cs` 中修改更新服务器地址：

```csharp
AutoUpdater.Start("https://your-server.com/launcher_version.xml");
```

#### 版本信息文件格式

服务器上的 `launcher_version.xml` 文件格式示例：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<item>
    <version>*******</version>
    <url>https://your-server.com/VIC_Launcher_CloudPlatform.zip</url>
    <changelog>https://your-server.com/changelog.html</changelog>
    <mandatory>false</mandatory>
</item>
```

## 部署指南

### 开发环境要求

- Visual Studio 2022 或更高版本
- .NET 8.0 SDK
- Windows 10/11

### 编译步骤

1. 克隆或下载项目源码
2. 使用 Visual Studio 打开 `LauncherAutoUpdater.sln`
3. 配置更新服务器地址（修改 `Program.cs` 中的 URL）
4. 编译项目（Release 模式）
5. 在 `bin\Release\net8.0-windows\` 目录中找到生成的可执行文件

### 发布部署

1. 将编译好的 `LauncherAutoUpdater.exe` 复制到目标目录
2. 确保目标目录中包含 `VIC_Launcher_CloudPlatform.exe`
3. 配置更新服务器并上传版本信息文件
4. 测试更新流程

## 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| 更新服务器地址 | 版本信息文件的 URL | 需要配置 |
| 下载临时目录 | 更新文件的临时存储位置 | `UpdateTemp` |
| 目标程序名称 | 要更新的程序文件名 | `VIC_Launcher_CloudPlatform.exe` |

## 故障排除

### 常见问题

1. **无法连接到更新服务器**
   - 检查网络连接
   - 验证服务器地址是否正确
   - 确认防火墙设置

2. **更新下载失败**
   - 检查磁盘空间是否充足
   - 验证下载链接是否有效
   - 检查文件权限

3. **程序启动失败**
   - 确认 `VIC_Launcher_CloudPlatform.exe` 文件存在
   - 检查文件是否被杀毒软件阻止
   - 验证文件完整性

## 开发说明

### 项目结构

```
LauncherAutoUpdater/
├── Program.cs              # 主程序入口和更新逻辑
├── Form1.cs               # 主窗体（可扩展UI功能）
├── Form1.Designer.cs      # 窗体设计器代码
├── LauncherAutoUpdater.csproj  # 项目配置文件
└── README.md              # 项目文档
```

### 扩展开发

- 可以在 `Form1.cs` 中添加更丰富的用户界面
- 支持添加配置文件来管理更新参数
- 可以集成日志记录功能
- 支持多语言界面

## 最新改进

### 已完成的功能优化

1. **统一命名空间**：修复了命名空间不一致的问题
2. **配置文件管理**：添加了 `UpdateConfig.json` 配置文件，支持灵活配置
3. **用户界面改进**：添加了状态显示和进度条
4. **错误处理增强**：完善的异常处理和用户友好的错误提示
5. **代码结构优化**：模块化设计，便于维护和扩展

### 新增文件

- `UpdateConfig.cs` - 配置管理类
- `UpdateConfig.json` - 配置文件
- `launcher_version_template.xml` - 服务器端版本信息模板
- `build.bat` - 自动构建脚本
- `test_setup.bat` - 测试环境设置脚本
- `DEPLOYMENT.md` - 详细部署指南

### 配置参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| UpdateServerUrl | 版本信息文件URL | 需要配置 | `https://your-server.com/launcher_version.xml` |
| TargetLauncherName | 目标程序名称 | `VIC_Launcher_CloudPlatform.exe` | 可自定义 |
| UpdateTempFolder | 临时下载目录 | `UpdateTemp` | 可自定义 |
| EnableAutoUpdate | 是否启用自动更新 | `true` | `true/false` |
| UpdateCheckInterval | 检查间隔（分钟） | `60` | 任意正整数 |
| ShowUpdateProgress | 是否显示进度 | `true` | `true/false` |

## 快速开始

### 1. 构建项目
```bash
# 使用构建脚本
build.bat

# 或手动构建
dotnet build --configuration Release
```

### 2. 设置测试环境
```bash
test_setup.bat
```

### 3. 配置更新服务器
编辑 `UpdateConfig.json` 文件，设置实际的服务器地址。

## 版本历史

- **v1.0.0**：基础自动更新功能
- **v1.1.0**：优化错误处理和用户体验
- **v1.2.0**：支持 VIC_Launcher_CloudPlatform 专用更新
- **v1.3.0**：添加配置文件管理和UI改进（当前版本）

## 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库：[GitHub Repository URL]
- 邮箱：[Your Email]
