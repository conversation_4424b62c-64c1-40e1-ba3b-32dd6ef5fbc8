@echo off
echo ========================================
echo VIC Launcher CloudPlatform 更新器测试环境设置
echo ========================================

REM 创建测试目录
set TEST_DIR=test_environment
if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
mkdir "%TEST_DIR%"

echo 正在复制更新器文件到测试目录...

REM 复制主要文件
copy "bin\Release\net8.0-windows\LauncherAutoUpdater.exe" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\LauncherAutoUpdater.dll" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\AutoUpdater.NET.dll" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\*.json" "%TEST_DIR%"

REM 复制配置文件
copy "UpdateConfig.json" "%TEST_DIR%"

REM 创建模拟的 VIC_Launcher_CloudPlatform.exe
echo @echo off > "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"
echo echo VIC Launcher CloudPlatform started! >> "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"
echo pause >> "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"

echo.
echo ========================================
echo 测试环境设置完成！
echo ========================================
echo 测试目录：%cd%\%TEST_DIR%
echo.
echo 测试步骤：
echo 1. 进入测试目录：cd %TEST_DIR%
echo 2. 运行更新器：LauncherAutoUpdater.exe
echo 3. 观察更新检查过程
echo.
echo 注意：
echo - 当前配置指向示例服务器，会显示连接错误（这是正常的）
echo - 请修改 UpdateConfig.json 中的服务器地址进行实际测试
echo - 模拟的 VIC_Launcher_CloudPlatform.exe 仅用于测试启动功能
echo.
pause
