using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using AutoUpdaterDotNET;

namespace LauncherAutoUpdater
{
    static class Program
    {
        // 配置对象
        private static readonly UpdateConfig _config = UpdateConfig.Load();

        // 主窗体实例
        private static Form1? _mainForm;

        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 验证配置
                if (!_config.IsValid())
                {
                    MessageBox.Show("配置文件无效，请检查 UpdateConfig.json 文件。", "配置错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Environment.Exit(1);
                    return;
                }

                // 创建主窗体
                _mainForm = new Form1();

                // 配置自动更新器
                ConfigureAutoUpdater();

                // 启动更新检查
                AutoUpdater.Start(_config.UpdateServerUrl);

                Application.Run(_mainForm); // 运行主窗体
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动更新器时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 配置自动更新器参数
        /// </summary>
        private static void ConfigureAutoUpdater()
        {
            // 设置下载临时目录
            AutoUpdater.DownloadPath = Path.Combine(Application.StartupPath, _config.UpdateTempFolder);

            // 注册更新完成事件
            AutoUpdater.ApplicationExitEvent += AutoUpdater_ApplicationExitEvent;

            // 设置更新器标题
            AutoUpdater.AppTitle = "VIC Launcher CloudPlatform 更新器";

            // 启用强制更新检查
            AutoUpdater.CheckForUpdateEvent += AutoUpdater_CheckForUpdateEvent;

            // 设置是否显示更新进度
            AutoUpdater.ShowSkipButton = false;
            AutoUpdater.ShowRemindLaterButton = false;

            // Unity项目特殊配置
            AutoUpdater.RunUpdateAsAdmin = false; // Unity项目通常不需要管理员权限
            AutoUpdater.ReportErrors = true; // 启用错误报告

            // 在更新前检查Unity进程
            // 注意：这里我们使用现有的事件处理器，Unity进程检查集成在其中
        }

        /// <summary>
        /// 更新检查事件处理
        /// </summary>
        private static void AutoUpdater_CheckForUpdateEvent(UpdateInfoEventArgs args)
        {
            if (args.Error != null)
            {
                _mainForm?.UpdateStatus($"检查更新失败：{args.Error.Message}");
                MessageBox.Show($"检查更新时发生错误：{args.Error.Message}", "更新错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!args.IsUpdateAvailable)
            {
                _mainForm?.UpdateStatus("已是最新版本，正在启动程序...");

                // 验证Unity项目完整性
                if (!UnityUpdateHelper.ValidateUnityProject(Application.StartupPath))
                {
                    _mainForm?.UpdateStatus("Unity项目文件不完整，请重新安装");
                    MessageBox.Show("Unity项目文件不完整，请重新安装程序。", "文件完整性检查",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 没有可用更新，直接启动目标程序
                StartTargetLauncher();
                Environment.Exit(0);
            }
            else
            {
                _mainForm?.UpdateStatus($"发现新版本 {args.CurrentVersion}，正在准备更新...");

                // 检查Unity进程是否在运行
                if (UnityUpdateHelper.IsUnityProcessRunning())
                {
                    var result = MessageBox.Show(
                        "检测到Unity相关进程正在运行，需要关闭这些进程才能继续更新。\n\n是否继续？",
                        "进程检查",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        _mainForm?.UpdateStatus("正在关闭Unity进程...");
                        if (!UnityUpdateHelper.TerminateUnityProcesses())
                        {
                            MessageBox.Show("无法关闭Unity进程，请手动关闭后重试。", "进程关闭失败",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        _mainForm?.UpdateStatus("用户取消更新");
                        return;
                    }
                }

                _mainForm?.UpdateStatus($"开始下载新版本 {args.CurrentVersion}...");
            }
        }

        /// <summary>
        /// 更新完成后的事件处理
        /// </summary>
        private static void AutoUpdater_ApplicationExitEvent()
        {
            try
            {
                StartTargetLauncher();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动 {_config.TargetLauncherName} 时发生错误：{ex.Message}", "启动错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Environment.Exit(0);
            }
        }

        /// <summary>
        /// 启动目标启动器程序
        /// </summary>
        private static void StartTargetLauncher()
        {
            string launcherPath = Path.Combine(Application.StartupPath, _config.TargetLauncherName);

            if (File.Exists(launcherPath))
            {
                ProcessStartInfo startInfo = new()
                {
                    FileName = launcherPath,
                    UseShellExecute = true,
                    WorkingDirectory = Application.StartupPath
                };

                Process.Start(startInfo);
            }
            else
            {
                MessageBox.Show($"找不到目标程序：{_config.TargetLauncherName}\n请确保文件存在于当前目录中。",
                    "文件未找到", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
