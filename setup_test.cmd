@echo off
chcp 65001 >nul
echo Setting up test environment...

set TEST_DIR=test_environment
if exist "%TEST_DIR%" rmdir /s /q "%TEST_DIR%"
mkdir "%TEST_DIR%"

echo Copying files...
copy "bin\Release\net8.0-windows\LauncherAutoUpdater.exe" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\LauncherAutoUpdater.dll" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\AutoUpdater.NET.dll" "%TEST_DIR%"
copy "bin\Release\net8.0-windows\*.json" "%TEST_DIR%"
copy "UpdateConfig.json" "%TEST_DIR%"

echo Creating mock VIC_Launcher_CloudPlatform.exe...
echo @echo off > "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"
echo echo VIC Launcher CloudPlatform started! >> "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"
echo pause >> "%TEST_DIR%\VIC_Launcher_CloudPlatform.exe"

echo.
echo Test environment ready in: %TEST_DIR%
echo To test: cd %TEST_DIR% and run LauncherAutoUpdater.exe
echo.
pause
