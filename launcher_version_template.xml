<?xml version="1.0" encoding="UTF-8"?>
<item>
    <!-- 版本号，格式：主版本.次版本.修订版本.构建版本 -->
    <version>*******</version>

    <!-- 下载地址，指向包含Unity项目完整构建文件的压缩包 -->
    <url>https://asset.vicfunxr.cn/launcher/cloud/updates/VIC_Launcher_CloudPlatform_v1.2.0.zip</url>

    <!-- 更新日志页面地址（可选） -->
    <changelog>https://asset.vicfunxr.cn/launcher/cloud/updates/changelog.html</changelog>

    <!-- 是否强制更新，true表示必须更新，false表示可选更新 -->
    <mandatory>false</mandatory>

    <!-- 更新说明（可选） -->
    <args>/S</args>

    <!-- 文件大小（字节，可选） -->
    <size>52428800</size>

    <!-- 文件校验和（MD5，可选，用于验证下载文件完整性） -->
    <checksum algorithm="MD5">5d41402abc4b2a76b9719d911017c592</checksum>

    <!-- Unity项目特殊配置 -->
    <!--
    压缩包应包含以下Unity构建文件结构：
    - VIC_Launcher_CloudPlatform.exe (主程序)
    - VIC_Launcher_CloudPlatform_Data/ (数据文件夹)
    - VIC_Launcher_CloudPlatform_BurstDebugInformation_DoNotShip/ (调试信息，可选)
    - MonoBleedingEdge/ (Mono运行时)
    - UnityCrashHandler64.exe (崩溃处理器)
    - UnityPlayer.dll (Unity播放器库)
    - 其他依赖文件
    -->
</item>
