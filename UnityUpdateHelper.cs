using System;
using System.IO;
using System.Diagnostics;
using System.Collections.Generic;
using System.Linq;

namespace LauncherAutoUpdater
{
    /// <summary>
    /// Unity项目更新辅助类
    /// </summary>
    public static class UnityUpdateHelper
    {
        /// <summary>
        /// Unity项目的关键文件和文件夹
        /// </summary>
        private static readonly string[] UnityEssentialItems = new[]
        {
            "VIC_Launcher_CloudPlatform.exe",
            "VIC_Launcher_CloudPlatform_Data",
            "MonoBleedingEdge",
            "UnityCrashHandler64.exe",
            "UnityPlayer.dll"
        };

        /// <summary>
        /// 可选的Unity文件和文件夹（如果存在则更新）
        /// </summary>
        private static readonly string[] UnityOptionalItems = new[]
        {
            "VIC_Launcher_CloudPlatform_BurstDebugInformation_DoNotShip"
        };

        /// <summary>
        /// 验证Unity项目文件完整性
        /// </summary>
        /// <param name="basePath">项目根目录</param>
        /// <returns>验证结果</returns>
        public static bool ValidateUnityProject(string basePath)
        {
            if (!Directory.Exists(basePath))
                return false;

            // 检查必需的文件和文件夹
            foreach (var item in UnityEssentialItems)
            {
                var fullPath = Path.Combine(basePath, item);
                if (!File.Exists(fullPath) && !Directory.Exists(fullPath))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取Unity项目的所有相关文件
        /// </summary>
        /// <param name="basePath">项目根目录</param>
        /// <returns>文件列表</returns>
        public static List<string> GetUnityProjectFiles(string basePath)
        {
            var files = new List<string>();

            if (!Directory.Exists(basePath))
                return files;

            // 添加必需文件
            foreach (var item in UnityEssentialItems)
            {
                var fullPath = Path.Combine(basePath, item);
                if (File.Exists(fullPath))
                {
                    files.Add(fullPath);
                }
                else if (Directory.Exists(fullPath))
                {
                    files.AddRange(Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories));
                }
            }

            // 添加可选文件
            foreach (var item in UnityOptionalItems)
            {
                var fullPath = Path.Combine(basePath, item);
                if (File.Exists(fullPath))
                {
                    files.Add(fullPath);
                }
                else if (Directory.Exists(fullPath))
                {
                    files.AddRange(Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories));
                }
            }

            return files;
        }

        /// <summary>
        /// 检查Unity进程是否正在运行
        /// </summary>
        /// <returns>是否有Unity相关进程在运行</returns>
        public static bool IsUnityProcessRunning()
        {
            try
            {
                var processes = Process.GetProcesses();
                return processes.Any(p => 
                    p.ProcessName.Contains("VIC_Launcher_CloudPlatform", StringComparison.OrdinalIgnoreCase) ||
                    p.ProcessName.Contains("Unity", StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 终止Unity相关进程
        /// </summary>
        /// <returns>是否成功终止所有进程</returns>
        public static bool TerminateUnityProcesses()
        {
            try
            {
                var processes = Process.GetProcesses()
                    .Where(p => p.ProcessName.Contains("VIC_Launcher_CloudPlatform", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                foreach (var process in processes)
                {
                    try
                    {
                        process.Kill();
                        process.WaitForExit(5000); // 等待最多5秒
                    }
                    catch
                    {
                        // 忽略单个进程终止失败
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建Unity项目备份
        /// </summary>
        /// <param name="sourcePath">源路径</param>
        /// <param name="backupPath">备份路径</param>
        /// <returns>是否备份成功</returns>
        public static bool CreateBackup(string sourcePath, string backupPath)
        {
            try
            {
                if (Directory.Exists(backupPath))
                {
                    Directory.Delete(backupPath, true);
                }

                Directory.CreateDirectory(backupPath);

                // 备份必需文件
                foreach (var item in UnityEssentialItems)
                {
                    var sourceItem = Path.Combine(sourcePath, item);
                    var backupItem = Path.Combine(backupPath, item);

                    if (File.Exists(sourceItem))
                    {
                        File.Copy(sourceItem, backupItem, true);
                    }
                    else if (Directory.Exists(sourceItem))
                    {
                        CopyDirectory(sourceItem, backupItem);
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 递归复制目录
        /// </summary>
        /// <param name="sourceDir">源目录</param>
        /// <param name="destDir">目标目录</param>
        private static void CopyDirectory(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (var dir in Directory.GetDirectories(sourceDir))
            {
                var destSubDir = Path.Combine(destDir, Path.GetFileName(dir));
                CopyDirectory(dir, destSubDir);
            }
        }

        /// <summary>
        /// 获取Unity项目版本信息
        /// </summary>
        /// <param name="basePath">项目根目录</param>
        /// <returns>版本信息</returns>
        public static string GetUnityProjectVersion(string basePath)
        {
            try
            {
                var exePath = Path.Combine(basePath, "VIC_Launcher_CloudPlatform.exe");
                if (File.Exists(exePath))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(exePath);
                    return versionInfo.FileVersion ?? "Unknown";
                }
            }
            catch
            {
                // 忽略版本获取错误
            }

            return "Unknown";
        }
    }
}
